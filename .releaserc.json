{"branches": ["master", {"name": "develop", "prerelease": "beta"}, {"name": "feature/*", "prerelease": "alpha"}], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "perf", "release": "patch"}, {"type": "revert", "release": "patch"}, {"type": "docs", "scope": "README", "release": "patch"}, {"type": "style", "release": false}, {"type": "refactor", "release": "patch"}, {"type": "test", "release": false}, {"type": "build", "release": "patch"}, {"type": "ci", "release": false}, {"scope": "no-release", "release": false}], "parserOpts": {"noteKeywords": ["BREAKING CHANGE", "BREAKING CHANGES"]}}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "🚀 Features"}, {"type": "fix", "section": "🐛 Bug Fixes"}, {"type": "perf", "section": "⚡ Performance Improvements"}, {"type": "revert", "section": "⏪ Reverts"}, {"type": "docs", "section": "📚 Documentation"}, {"type": "style", "section": "💎 Styles", "hidden": true}, {"type": "refactor", "section": "📦 Code Refactoring"}, {"type": "test", "section": "🚨 Tests", "hidden": true}, {"type": "build", "section": "🛠 Build System"}, {"type": "ci", "section": "⚙️ Continuous Integration", "hidden": true}]}}], ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md", "changelogTitle": "# Changelog\n\nAll notable changes to this project will be documented in this file. See [Conventional Commits](https://conventionalcommits.org) for commit guidelines."}], ["@semantic-release/npm", {"npmPublish": false}], ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json", "package-lock.json"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], ["@semantic-release/github", {"successComment": "🎉 This ${issue.pull_request ? 'PR is included' : 'issue has been resolved'} in version ${nextRelease.version} :tada:", "failTitle": "❌ The automated release is failing 🚨", "failComment": "The release from branch `${branch.name}` had failed due to the following errors:\n- ${errors.map(err => err.message).join('\\n- ')}", "labels": false, "releasedLabels": false}]]}