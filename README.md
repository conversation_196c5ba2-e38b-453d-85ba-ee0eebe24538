# Paragon API

A Node.js API built with Express, Sequelize, and PostgreSQL.

## 🚀 Features

- RESTful API architecture
- JWT authentication
- Database migrations and seeders
- Comprehensive testing with Jest
- Code quality tools (ESLint, Prettier)
- Automated releases with semantic-release
- CI/CD pipeline with GitHub Actions

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL 12+
- npm or yarn

## 🛠 Installation

1. Clone the repository:
```bash
git clone https://github.com/rakamindev/paragon-api.git
cd paragon-api
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Set up the database:
```bash
npm run db:create
npm run db:migrate
npm run db:seed
```

## 🏃‍♂️ Running the Application

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

## 📝 Code Quality

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format

# Check formatting
npm run format:check
```

## 🔄 Release Process

This project uses [semantic-release](https://semantic-release.gitbook.io/) for automated versioning and releases.

### Commit Message Convention

We follow the [Conventional Commits](https://conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### Types:
- `feat`: A new feature (triggers minor release)
- `fix`: A bug fix (triggers patch release)
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `build`: Changes that affect the build system or external dependencies
- `ci`: Changes to our CI configuration files and scripts
- `chore`: Other changes that don't modify src or test files
- `revert`: Reverts a previous commit

#### Examples:
```bash
feat: add user authentication endpoint
fix: resolve database connection timeout
docs: update API documentation
feat!: change user model structure (BREAKING CHANGE)
```

### Making Commits

Use commitizen for guided commit messages:
```bash
npm run commit
```

### Release Branches

- `master`: Production releases (latest stable)
- `develop`: Pre-releases (beta versions)
- `feature/*`: Alpha releases for feature branches

### Automated Releases

Releases are automatically created when:
1. Code is pushed to `master`, `develop`, or `feature/*` branches
2. All tests pass
3. Code quality checks pass
4. Commit messages follow conventional format

### Manual Release (if needed)

```bash
# Dry run to see what would be released
npm run semantic-release:dry-run

# Manual release (not recommended)
npm run release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes following our coding standards
4. Write tests for your changes
5. Commit using conventional commits: `npm run commit`
6. Push to your branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Development Workflow

1. **Create a feature branch** from `develop`
2. **Make changes** with proper tests
3. **Use conventional commits** for all changes
4. **Ensure all checks pass** (tests, linting, formatting)
5. **Create a Pull Request** to `develop`
6. **After review and approval**, merge to `develop`
7. **For production releases**, create PR from `develop` to `master`

## 📊 CI/CD Pipeline

Our GitHub Actions workflow includes:

- **Continuous Integration**: Automated testing, linting, and security checks
- **Continuous Deployment**: Automated releases based on semantic versioning
- **Quality Gates**: Code coverage, security audits, dependency reviews

### Workflow Triggers

- **Push to any branch**: Runs tests and quality checks
- **Pull Request**: Runs full CI pipeline including dependency review
- **Push to release branches**: Triggers automated release process

## 📚 Documentation

- [API Documentation](./docs/README.md)
- [Development Guide](./docs/development.md)
- [Testing Guide](./docs/testing.md)

## 📄 License

This project is licensed under the ISC License.

## 🆘 Support

If you encounter any issues or have questions, please:
1. Check existing [Issues](https://github.com/rakamindev/paragon-api/issues)
2. Create a new issue with detailed information
3. Follow our issue templates for bug reports and feature requests
