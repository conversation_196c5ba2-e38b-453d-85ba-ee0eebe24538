# Output Formatting

The output formatting system provides consistent response formatting across all
API endpoints. All output classes extend the `ApiOutput` base class to ensure
uniform JSON responses.

## ApiOutput Base Class

The `ApiOutput` class provides:

- **Consistent Response Format**: All responses follow the same JSON structure
- **Single Item Responses**: `renderJson()` for individual resources
- **Array Responses**: `renderJsonArray()` for collections
- **Custom Status Codes**: Support for different HTTP status codes
- **Flexible Formatting**: Custom format methods for different response types

### Response Structure

All API responses follow this structure:

```json
{
  "data": {
    // Formatted resource data
  }
}
```

For arrays:

```json
{
  "data": [
    // Array of formatted resources
  ]
}
```

## Output Class Structure

### Basic Output Pattern

```javascript
const ApiOutput = require('./ApiOutput');

class SomeOutput extends ApiOutput {
  /**
   * Format the output data
   * @param {Object} item - Optional item for array formatting
   * @returns {Object} Formatted data
   */
  format(item = null) {
    // Use item parameter for array formatting, or this.data for single items
    const data = item || this.data;

    return {
      id: data.id,
      name: data.name,
      email: data.email,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  }
}

module.exports = SomeOutput;
```

### Usage in Controllers

```javascript
// Single item response
const output = new UserOutput(user);
output.renderJson(res);

// Array response
const output = new UserOutput(users);
output.renderJsonArray(res);

// Custom status code
const output = new UserOutput(user, { statusCode: 201 });
output.renderJson(res);
```

## Creating a New Output Class

### Step 1: Create the Output File

Create a new file in `src/outputs/` following the naming convention
`{Entity}Output.js`:

```javascript
// src/outputs/ProductOutput.js
const ApiOutput = require('./ApiOutput');

class ProductOutput extends ApiOutput {
  /**
   * Format product data for API response
   * @param {Object} item - Product data (for array formatting)
   * @returns {Object} Formatted product data
   */
  format(item = null) {
    const product = item || this.data;

    return {
      id: product.id,
      name: product.name,
      description: product.description,
      price: parseFloat(product.price), // Ensure proper number format
      category: product.category,
      tags: product.tags || [],
      isActive: product.isActive,
      createdAt: product.created_at,
      updatedAt: product.updated_at,
      // Computed fields
      priceFormatted: `$${parseFloat(product.price).toFixed(2)}`,
    };
  }
}

module.exports = ProductOutput;
```

### Step 2: Use in Controller

```javascript
// src/controllers/ProductController.js
const ProductOutput = require('../outputs/ProductOutput');

class ProductController extends ApiController {
  show = this.createMethod(async (req, res) => {
    const { id } = req.params;
    const product = await this.service.findById(id);

    const output = new ProductOutput(product);
    output.renderJson(res);
  });

  index = this.createMethod(async (req, res) => {
    const products = await this.service.findAll();

    const output = new ProductOutput(products);
    output.renderJsonArray(res);
  });
}
```

## Advanced Output Patterns

### Nested Resource Formatting

```javascript
// src/outputs/OrderOutput.js
const ApiOutput = require('./ApiOutput');
const UserOutput = require('./UserOutput');
const ProductOutput = require('./ProductOutput');

class OrderOutput extends ApiOutput {
  format(item = null) {
    const order = item || this.data;

    return {
      id: order.id,
      orderNumber: order.order_number,
      status: order.status,
      totalAmount: parseFloat(order.total_amount),

      // Nested user data
      customer: this.formatUser(order.User),

      // Nested array of items
      items: order.OrderItems
        ? order.OrderItems.map(item => this.formatOrderItem(item))
        : [],

      createdAt: order.created_at,
      updatedAt: order.updated_at,
    };
  }

  /**
   * Format nested user data
   * @param {Object} user - User object
   * @returns {Object} Formatted user data
   */
  formatUser(user) {
    if (!user) return null;

    const userOutput = new UserOutput(user);
    return userOutput.format();
  }

  /**
   * Format order item data
   * @param {Object} item - Order item object
   * @returns {Object} Formatted order item data
   */
  formatOrderItem(item) {
    return {
      id: item.id,
      quantity: item.quantity,
      price: parseFloat(item.price),
      subtotal: parseFloat(item.quantity * item.price),
      product: this.formatProduct(item.Product),
    };
  }

  /**
   * Format nested product data
   * @param {Object} product - Product object
   * @returns {Object} Formatted product data
   */
  formatProduct(product) {
    if (!product) return null;

    const productOutput = new ProductOutput(product);
    return productOutput.format();
  }
}

module.exports = OrderOutput;
```

### Conditional Field Formatting

```javascript
class UserOutput extends ApiOutput {
  format(item = null) {
    const user = item || this.data;
    const options = this.options || {};

    const baseData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      createdAt: user.created_at,
    };

    // Include sensitive data only for admin users or self
    if (options.includePrivate) {
      baseData.lastLoginAt = user.last_login_at;
      baseData.isEmailVerified = user.is_email_verified;
    }

    // Include admin-only fields
    if (options.includeAdmin) {
      baseData.isActive = user.is_active;
      baseData.loginCount = user.login_count;
    }

    return baseData;
  }
}

// Usage in controller
show = this.createMethod(async (req, res) => {
  const user = await this.service.findById(req.params.id);
  const currentUser = req.user; // From auth middleware

  const options = {
    includePrivate: currentUser.id === user.id || currentUser.role === 'admin',
    includeAdmin: currentUser.role === 'admin',
  };

  const output = new UserOutput(user, options);
  output.renderJson(res);
});
```

### Pagination Support

```javascript
class ProductOutput extends ApiOutput {
  format(item = null) {
    const product = item || this.data;

    return {
      id: product.id,
      name: product.name,
      price: parseFloat(product.price),
      // ... other fields
    };
  }

  /**
   * Render paginated array response
   * @param {Object} res - Express response object
   */
  renderPaginatedJson(res) {
    const status = this.options.statusCode || 200;
    const pagination = this.options.pagination || {};

    const data = this.data.map(item => this.format(item));

    res.status(status).json({
      data,
      pagination: {
        page: pagination.page || 1,
        limit: pagination.limit || 10,
        total: pagination.total || data.length,
        totalPages: Math.ceil(
          (pagination.total || data.length) / (pagination.limit || 10),
        ),
      },
    });
  }
}

// Usage in controller
index = this.createMethod(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  const result = await this.service.findAll({ page, limit });

  const output = new ProductOutput(result.items, {
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.total,
    },
  });

  output.renderPaginatedJson(res);
});
```

### Error Response Formatting

```javascript
// Custom error output for specific endpoints
class ValidationErrorOutput extends ApiOutput {
  format() {
    const error = this.data;

    return {
      message: error.message,
      field: error.field,
      code: error.code,
      suggestions: error.suggestions || []
    };
  }
}

// Usage in controller error handling
catch (error) {
  if (error instanceof ValidationError) {
    const output = new ValidationErrorOutput(error);
    return output.renderJson(res, { statusCode: 422 });
  }

  // Fall back to default error handling
  this.handleError(error, res, next);
}
```

## Response Status Codes

### Common Status Codes

```javascript
// Success responses
const output = new SomeOutput(data, { statusCode: 200 }); // OK (default)
const output = new SomeOutput(data, { statusCode: 201 }); // Created
const output = new SomeOutput(data, { statusCode: 202 }); // Accepted

// No content response
res.status(204).send(); // No Content (for DELETE operations)

// Client error responses (handled by ApiController)
// 400 - Bad Request (InvalidError)
// 401 - Unauthorized (UnauthorizedError)
// 403 - Forbidden (ForbiddenError)
// 404 - Not Found (NotFoundError)
// 422 - Unprocessable Entity (custom validation errors)

// Server error responses
// 500 - Internal Server Error (unhandled errors)
```

### Custom Status Code Usage

```javascript
// Created resource
create = this.createMethod(async (req, res) => {
  const result = await this.service.create(input.output());
  const output = new SomeOutput(result, { statusCode: 201 });
  output.renderJson(res);
});

// Accepted for processing
processAsync = this.createMethod(async (req, res) => {
  await this.service.queueForProcessing(input.output());
  const output = new ProcessingOutput(
    { message: 'Queued for processing' },
    { statusCode: 202 },
  );
  output.renderJson(res);
});
```

## Testing Output Classes

```javascript
// tests/outputs/ProductOutput.test.js
const ProductOutput = require('../../src/outputs/ProductOutput');

describe('ProductOutput', () => {
  const mockProduct = {
    id: 1,
    name: 'Test Product',
    price: '99.99',
    category: 'electronics',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  describe('format', () => {
    it('should format product data correctly', () => {
      const output = new ProductOutput(mockProduct);
      const formatted = output.format();

      expect(formatted).toEqual({
        id: 1,
        name: 'Test Product',
        price: 99.99,
        category: 'electronics',
        priceFormatted: '$99.99',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      });
    });
  });

  describe('renderJson', () => {
    it('should render JSON response with correct structure', () => {
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      const output = new ProductOutput(mockProduct);
      output.renderJson(mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          id: 1,
          name: 'Test Product',
        }),
      });
    });
  });
});
```

## Best Practices

### 1. Keep Formatting Logic in Output Classes

```javascript
// ✅ Good - formatting in output class
class ProductOutput extends ApiOutput {
  format(item = null) {
    const product = item || this.data;
    return {
      id: product.id,
      priceFormatted: `$${parseFloat(product.price).toFixed(2)}`,
    };
  }
}

// ❌ Bad - formatting in controller
create = this.createMethod(async (req, res) => {
  const product = await this.service.create(input.output());

  // Don't format data in controller
  const formatted = {
    ...product,
    priceFormatted: `$${product.price.toFixed(2)}`,
  };

  res.json({ data: formatted });
});
```

### 2. Use Consistent Field Naming

```javascript
// ✅ Good - consistent camelCase in API responses
format() {
  return {
    id: data.id,
    firstName: data.first_name,    // Convert snake_case to camelCase
    lastName: data.last_name,
    createdAt: data.created_at,
    updatedAt: data.updated_at
  };
}
```

### 3. Handle Null/Undefined Values

```javascript
format(item = null) {
  const data = item || this.data;

  return {
    id: data.id,
    name: data.name || '',
    description: data.description || null,
    tags: data.tags || [],
    user: data.User ? this.formatUser(data.User) : null
  };
}
```

### 4. Reuse Output Classes

```javascript
// ✅ Good - reuse existing output classes
class OrderOutput extends ApiOutput {
  format(item = null) {
    const order = item || this.data;

    return {
      id: order.id,
      customer: order.User ? new UserOutput(order.User).format() : null,
      items: order.Items
        ? order.Items.map(item => new ProductOutput(item).format())
        : [],
    };
  }
}
```

### 5. Document Complex Formatting

```javascript
class ReportOutput extends ApiOutput {
  /**
   * Format report data with calculated metrics
   * @param {Object} item - Report data
   * @returns {Object} Formatted report with metrics
   */
  format(item = null) {
    const report = item || this.data;

    return {
      id: report.id,
      title: report.title,
      // Calculate percentage change
      growthRate: this.calculateGrowthRate(report.current, report.previous),
      // Format currency values
      revenue: this.formatCurrency(report.revenue),
      // ... other formatted fields
    };
  }

  /**
   * Calculate growth rate percentage
   * @private
   */
  calculateGrowthRate(current, previous) {
    if (!previous || previous === 0) return 0;
    return (((current - previous) / previous) * 100).toFixed(2);
  }

  /**
   * Format currency values
   * @private
   */
  formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }
}
```
