# Developer Documentation

Welcome to the Paragon API developer documentation. This guide covers all the
non-business technical aspects of the codebase to help new developers understand
how the system works and how to contribute effectively.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Controllers](./controllers.md) - How controllers work and how to create new
   ones
3. [Input Validation](./inputs.md) - Input validation system and creating new
   input classes
4. [Output Formatting](./outputs.md) - Response formatting and creating new
   output classes
5. [Models](./models.md) - Database models, Rails-style conventions, and
   associations
6. [Services](./services.md) - Business logic layer and service patterns
7. [Error Handling](./error-handling.md) - Custom errors and error handling
   patterns
8. [Middleware](./middleware.md) - Rate limiting and custom middleware
9. [Routing](./routing.md) - API structure and route organization
10. [Testing](./testing.md) - Testing framework, patterns, and best practices
11. [Development Workflow](./development.md) - Setup, configuration, and
    development processes

## Architecture Overview

This is a Node.js REST API built with Express.js following a layered
architecture pattern inspired by Rails conventions. The application is
structured to separate concerns and provide a clean, maintainable codebase.

### Key Design Principles

- **Convention over Configuration**: Following Rails-style conventions for
  consistency
- **Separation of Concerns**: Clear separation between controllers, services,
  models, and utilities
- **Input/Output Transformation**: Dedicated classes for request validation and
  response formatting
- **Centralized Error Handling**: Consistent error handling across all endpoints
- **Base Classes**: Abstract base classes that handle common functionality

### Directory Structure

```
src/
├── app.js              # Express app configuration
├── server.js           # Server startup and graceful shutdown
├── config/             # Configuration files
├── controllers/        # Request handlers (extend ApiController)
├── inputs/             # Input validation classes (extend ApplicationInput)
├── outputs/            # Response formatting classes (extend ApiOutput)
├── models/             # Database models (extend AppModel)
├── services/           # Business logic layer (extend AppService)
├── middlewares/        # Custom middleware
├── routes/             # Route definitions
├── errors/             # Custom error classes
├── migrations/         # Database migrations
├── seeders/            # Database seeders
└── utils/              # Utility functions
```

### Request Flow

1. **Route** → Defines the endpoint and maps to controller method
2. **Middleware** → Rate limiting, authentication, etc.
3. **Controller** → Handles the request, delegates to service
4. **Input** → Validates and transforms request data
5. **Service** → Contains business logic, interacts with models
6. **Model** → Database operations and data validation
7. **Output** → Formats response data
8. **Response** → JSON response sent to client

### Technology Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: PostgreSQL with Sequelize ORM
- **Validation**: AJV (JSON Schema)
- **Authentication**: JWT
- **Testing**: Jest with Supertest
- **Code Quality**: ESLint + Prettier
- **Process Management**: PM2 (production)

### Base Classes

The application uses several base classes that provide common functionality:

- **ApiController**: Base controller with error handling and method wrapping
- **ApplicationInput**: Base input class with JSON schema validation
- **ApiOutput**: Base output class with response formatting
- **AppModel**: Base model class with Rails-style conventions
- **AppService**: Base service class with common utilities

## Getting Started

1. Read through this documentation to understand the architecture
2. Set up your development environment following the
   [Development Workflow](./development.md)
3. Look at existing examples in each layer to understand the patterns
4. Follow the conventions when adding new functionality

## Contributing

When adding new functionality:

1. Follow the established patterns and conventions
2. Extend the appropriate base classes
3. Add comprehensive tests
4. Update documentation as needed
5. Ensure code passes linting and formatting checks

## Need Help?

- Check the specific documentation for each component
- Look at existing code examples
- Review the test files for usage patterns
- Ask questions in team channels
