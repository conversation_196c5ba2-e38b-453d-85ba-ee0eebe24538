# Error Handling

The application uses a centralized error handling system with custom error
classes that map to appropriate HTTP status codes. All errors are handled
consistently across the API.

## Error Architecture

### Custom Error Classes

The application defines several custom error classes that extend the native
JavaScript `Error`:

- **InvalidError** (400) - Validation errors, bad request data
- **UnauthorizedError** (401) - Authentication required
- **ForbiddenError** (403) - Access denied
- **NotFoundError** (404) - Resource not found

### Error Flow

1. **Error Thrown** → Custom error thrown in service/controller
2. **ApiController** → Catches error in `createMethod()` wrapper
3. **Error Handler** → Maps error type to HTTP status code
4. **JSON Response** → Consistent error response format

## Custom Error Classes

### InvalidError (400 Bad Request)

Used for validation errors and invalid request data:

```javascript
// src/errors/InvalidError.js
class InvalidError extends Error {
  constructor(message, options = {}) {
    super(message);
    this.name = 'InvalidError';
    this.statusCode = 400;
    this.details = options.details;
  }
}

module.exports = InvalidError;
```

**Usage Examples:**

```javascript
const InvalidError = require('../errors/InvalidError');

// Simple validation error
throw new InvalidError('Email is required');

// Validation error with details
throw new InvalidError('Validation failed', {
  details: [
    { field: 'email', message: 'Invalid email format' },
    { field: 'password', message: 'Password too short' },
  ],
});

// In services using AppService helper
this.assert(user.age >= 18, 'User must be at least 18 years old');
```

### UnauthorizedError (401 Unauthorized)

Used when authentication is required but not provided:

```javascript
// src/errors/UnauthorizedError.js
class UnauthorizedError extends Error {
  constructor(message) {
    super(message);
    this.name = 'UnauthorizedError';
    this.statusCode = 401;
  }
}

module.exports = UnauthorizedError;
```

**Usage Examples:**

```javascript
const UnauthorizedError = require('../errors/UnauthorizedError');

// Invalid credentials
throw new UnauthorizedError('Invalid email or password');

// Missing authentication
throw new UnauthorizedError('Authentication required');

// Expired token
throw new UnauthorizedError('Token has expired');
```

### ForbiddenError (403 Forbidden)

Used when user is authenticated but lacks permission:

```javascript
// src/errors/ForbiddenError.js
class ForbiddenError extends Error {
  constructor(message) {
    super(message);
    this.name = 'ForbiddenError';
    this.statusCode = 403;
  }
}

module.exports = ForbiddenError;
```

**Usage Examples:**

```javascript
const ForbiddenError = require('../errors/ForbiddenError');

// Insufficient permissions
throw new ForbiddenError('Admin access required');

// Resource access denied
throw new ForbiddenError('You can only edit your own profile');

// Feature not available
throw new ForbiddenError('This feature is not available in your plan');
```

### NotFoundError (404 Not Found)

Used when a requested resource doesn't exist:

```javascript
// src/errors/NotFoundError.js
class NotFoundError extends Error {
  constructor(message) {
    super(message);
    this.name = 'NotFoundError';
    this.statusCode = 404;
  }
}

module.exports = NotFoundError;
```

**Usage Examples:**

```javascript
const NotFoundError = require('../errors/NotFoundError');

// Resource not found
throw new NotFoundError('User not found');

// Endpoint not found
throw new NotFoundError('The requested endpoint does not exist');

// In services using AppService helper
const user = await User.findByPk(id);
this.exists(user, 'User not found');
```

## Error Response Format

All errors return a consistent JSON structure:

```json
{
  "error": "Error message",
  "details": [], // Array of detailed error information (optional)
  "backtrace": [] // Stack trace (development only)
}
```

### Examples

**Validation Error (400):**

```json
{
  "error": "Validation failed",
  "details": [
    {
      "instancePath": "/email",
      "schemaPath": "#/properties/email/format",
      "keyword": "format",
      "params": { "format": "email" },
      "message": "must match format \"email\""
    }
  ],
  "backtrace": [
    "Error: Validation failed",
    "    at ApplicationInput.validate..."
  ]
}
```

**Authentication Error (401):**

```json
{
  "error": "Invalid email or password",
  "details": [],
  "backtrace": []
}
```

**Not Found Error (404):**

```json
{
  "error": "User not found",
  "details": [],
  "backtrace": []
}
```

**Server Error (500):**

```json
{
  "error": "Internal server error",
  "detail": undefined,
  "backtrace": ["Error: Database connection failed", "    at ..."]
}
```

## Error Handling in Controllers

### ApiController Error Handler

The `ApiController` base class automatically handles all errors:

```javascript
// src/controllers/ApiController.js
class ApiController {
  createMethod(func) {
    return async (req, res, next) => {
      try {
        await func(req, res, next);
      } catch (error) {
        this.handleError(error, res, next);
      }
    };
  }

  handleError(error, res, next) {
    let backtrace = [];
    if (process.env.NODE_ENV !== 'production') {
      backtrace = error.stack.split('\n').map(line => line.trim());
    }

    // If response already sent, pass to Express error handler
    if (res.headersSent) {
      return next(error);
    }

    // Handle custom errors
    if (error instanceof InvalidError) {
      return res.status(400).json({
        error: error.message || 'Validation failed',
        details: error.details,
        backtrace,
      });
    }

    if (error instanceof UnauthorizedError) {
      return res.status(401).json({
        error: error.message || 'Unauthorized',
        details: [],
        backtrace,
      });
    }

    if (error instanceof ForbiddenError) {
      return res.status(403).json({
        error: error.message || 'Forbidden',
        details: [],
        backtrace,
      });
    }

    if (error instanceof NotFoundError) {
      return res.status(404).json({
        error: error.message || 'Not found',
        details: [],
        backtrace,
      });
    }

    // Handle unexpected errors
    console.error('Controller error:', error);
    res.status(500).json({
      error: 'Internal server error',
      detail: undefined,
      backtrace,
    });
  }
}
```

### Controller Method Pattern

Always wrap controller methods with `createMethod()`:

```javascript
class UserController extends ApiController {
  // ✅ Correct - errors are automatically handled
  show = this.createMethod(async (req, res) => {
    const { id } = req.params;
    const user = await this.service.findById(id); // May throw NotFoundError

    const output = new UserOutput(user);
    output.renderJson(res);
  });

  // ❌ Wrong - errors won't be handled properly
  async show(req, res) {
    try {
      const { id } = req.params;
      const user = await this.service.findById(id);

      const output = new UserOutput(user);
      output.renderJson(res);
    } catch (error) {
      // Manual error handling - inconsistent
      res.status(500).json({ error: error.message });
    }
  }
}
```

## Error Handling in Services

### Using AppService Helpers

The `AppService` base class provides helper methods:

```javascript
class UserService extends AppService {
  async findById(id) {
    const user = await User.findByPk(id);

    // Throws NotFoundError if user is null/undefined
    this.exists(user, 'User not found');

    return user;
  }

  async create(data) {
    // Check for existing user
    const existing = await User.findOne({ where: { email: data.email } });

    // Throws InvalidError if assertion fails
    this.assert(!existing, 'Email already exists');

    return User.create(data);
  }

  async updatePassword(userId, oldPassword, newPassword) {
    const user = await this.findById(userId);

    // Verify old password
    const isValid = await user.verifyPassword(oldPassword);
    this.assert(isValid, 'Current password is incorrect');

    // Validate new password
    this.assert(
      newPassword.length >= 8,
      'Password must be at least 8 characters',
    );

    // Update password
    await user.update({ password: newPassword });
    return user;
  }
}
```

### Manual Error Throwing

```javascript
class OrderService extends AppService {
  async createOrder(orderData) {
    // Validate order items
    if (!orderData.items || orderData.items.length === 0) {
      throw new InvalidError('Order must contain at least one item');
    }

    // Check stock availability
    for (const item of orderData.items) {
      const product = await Product.findByPk(item.productId);

      if (!product) {
        throw new NotFoundError(`Product ${item.productId} not found`);
      }

      if (product.stockQuantity < item.quantity) {
        throw new InvalidError(`Insufficient stock for ${product.name}`);
      }
    }

    // Create order...
  }
}
```

## Input Validation Errors

The `ApplicationInput` class automatically throws `InvalidError` for validation
failures:

```javascript
// src/inputs/ApplicationInput.js
validate() {
  const schema = this.schema();
  const validate = this.ajv.compile(schema);
  const dataToValidate = JSON.parse(JSON.stringify(this.data));

  this.isValid = validate(dataToValidate);

  if (this.isValid) {
    this.validatedData = dataToValidate;
    this.errors = [];
    return true;
  }

  this.errors = validate.errors;
  throw new InvalidError('Validation failed', {
    details: this.errors,
  });
}
```

**Example validation error details:**

```javascript
[
  {
    instancePath: '/email',
    schemaPath: '#/properties/email/format',
    keyword: 'format',
    params: { format: 'email' },
    message: 'must match format "email"',
  },
  {
    instancePath: '/password',
    schemaPath: '#/properties/password/minLength',
    keyword: 'minLength',
    params: { limit: 8 },
    message: 'must NOT have fewer than 8 characters',
  },
];
```

## Database Errors

Handle Sequelize database errors appropriately:

```javascript
class UserService extends AppService {
  async create(data) {
    try {
      return await User.create(data);
    } catch (error) {
      // Handle unique constraint violations
      if (error.name === 'SequelizeUniqueConstraintError') {
        const field = error.errors[0].path;
        throw new InvalidError(`${field} already exists`);
      }

      // Handle validation errors
      if (error.name === 'SequelizeValidationError') {
        const details = error.errors.map(err => ({
          field: err.path,
          message: err.message,
        }));
        throw new InvalidError('Validation failed', { details });
      }

      // Re-throw unexpected errors
      throw error;
    }
  }
}
```

## Testing Error Handling

### Testing Custom Errors

```javascript
// tests/services/UserService.test.js
describe('UserService', () => {
  describe('findById', () => {
    it('should throw NotFoundError when user not found', async () => {
      await expect(service.findById(999)).rejects.toThrow(NotFoundError);
      await expect(service.findById(999)).rejects.toThrow('User not found');
    });
  });

  describe('create', () => {
    it('should throw InvalidError for duplicate email', async () => {
      await User.create({ email: '<EMAIL>', name: 'Test' });

      const userData = { email: '<EMAIL>', name: 'Test 2' };
      await expect(service.create(userData)).rejects.toThrow(InvalidError);
      await expect(service.create(userData)).rejects.toThrow(
        'Email already exists',
      );
    });
  });
});
```

### Testing Controller Error Responses

```javascript
// tests/controllers/UserController.test.js
describe('UserController', () => {
  describe('GET /users/:id', () => {
    it('should return 404 for non-existent user', async () => {
      const response = await request(app).get('/api/v1/users/999');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('User not found');
    });
  });

  describe('POST /users', () => {
    it('should return 400 for invalid data', async () => {
      const invalidData = { email: 'invalid-email' };

      const response = await request(app)
        .post('/api/v1/users')
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details).toBeInstanceOf(Array);
    });
  });
});
```

## Best Practices

### 1. Use Appropriate Error Types

```javascript
// ✅ Good - specific error types
throw new NotFoundError('User not found'); // 404
throw new InvalidError('Email already exists'); // 400
throw new UnauthorizedError('Invalid credentials'); // 401
throw new ForbiddenError('Admin access required'); // 403

// ❌ Bad - generic errors
throw new Error('Something went wrong'); // 500
```

### 2. Provide Helpful Error Messages

```javascript
// ✅ Good - descriptive messages
throw new InvalidError('Password must be at least 8 characters long');
throw new NotFoundError('Product with ID 123 not found');

// ❌ Bad - vague messages
throw new InvalidError('Invalid input');
throw new NotFoundError('Not found');
```

### 3. Include Relevant Details

```javascript
// ✅ Good - detailed validation errors
throw new InvalidError('Validation failed', {
  details: [
    { field: 'email', message: 'Invalid email format' },
    { field: 'age', message: 'Must be at least 18' },
  ],
});

// ❌ Bad - no context
throw new InvalidError('Validation failed');
```

### 4. Handle Async Errors Properly

```javascript
// ✅ Good - wrapped in createMethod
methodName = this.createMethod(async (req, res) => {
  await this.service.someAsyncOperation(); // Errors caught automatically
});

// ❌ Bad - unhandled async errors
async methodName(req, res) {
  await this.service.someAsyncOperation(); // Errors may crash the app
}
```

### 5. Log Unexpected Errors

```javascript
// In ApiController.handleError()
if (!(error instanceof CustomError)) {
  console.error('Unexpected error:', error);
  // Consider using a proper logging service in production
}
```

### 6. Don't Expose Sensitive Information

```javascript
// ✅ Good - safe error messages
catch (error) {
  if (error.name === 'SequelizeConnectionError') {
    throw new Error('Database temporarily unavailable');
  }
}

// ❌ Bad - exposes internal details
catch (error) {
  throw new Error(`Database error: ${error.message}`);
  // Could expose connection strings, table names, etc.
}
```
