# Models

The model layer uses Sequelize ORM with a Rails-inspired architecture. All
models extend the `AppModel` base class, which handles Sequelize complexity and
provides Rails-style conventions.

## AppModel Base Class

The `AppModel` class provides:

- **Rails-style Conventions**: Automatic table naming, timestamps, and field
  naming
- **Simplified Model Definition**: Child classes only define `schema()`,
  `hooks()`, and `associate()`
- **Automatic Timestamps**: `created_at` and `updated_at` fields with automatic
  updates
- **Consistent Initialization**: Standardized model setup across the application

### Key Features

- **Table Naming**: Converts `UserProfile` → `user_profiles` automatically
- **Timestamp Management**: Handles `created_at` and `updated_at` automatically
- **Hook Integration**: Merges custom hooks with default timestamp hooks
- **Schema Validation**: Built-in Sequelize validation support

## Model Structure

### Basic Model Pattern

```javascript
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class SomeModel extends AppModel {
  /**
   * Define the model schema
   * @returns {Object} Sequelize model attributes
   */
  static schema() {
    return {
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [1, 255],
        },
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true,
        },
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
    };
  }

  /**
   * Define model associations
   * @param {Object} models - All models
   */
  static associate(models) {
    // Define relationships here
    this.hasMany(models.Order, {
      foreignKey: 'user_id',
      as: 'orders',
    });
  }

  /**
   * Define lifecycle hooks
   * @returns {Object} Sequelize hooks
   */
  static hooks() {
    return {
      beforeCreate: async instance => {
        // Custom logic before creating
        instance.email = instance.email.toLowerCase();
      },
      afterUpdate: async instance => {
        // Custom logic after updating
        console.log(`Updated ${instance.name}`);
      },
    };
  }

  /**
   * Additional model options (optional)
   * @returns {Object} Sequelize model options
   */
  static options() {
    return {
      indexes: [
        {
          unique: true,
          fields: ['email'],
        },
      ],
    };
  }

  // Instance methods
  getFullName() {
    return `${this.firstName} ${this.lastName}`;
  }
}

module.exports = SomeModel;
```

## Creating a New Model

### Step 1: Create the Model File

Create a new file in `src/models/` following the naming convention
`{EntityName}.js`:

```javascript
// src/models/Product.js
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class Product extends AppModel {
  static schema() {
    return {
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [1, 255],
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        validate: {
          min: 0,
        },
      },
      category: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          isIn: [['electronics', 'clothing', 'books', 'home']],
        },
      },
      sku: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
          notEmpty: true,
        },
      },
      stockQuantity: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        validate: {
          min: 0,
        },
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      tags: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: [],
      },
    };
  }

  static associate(models) {
    // Product belongs to a category
    this.belongsTo(models.Category, {
      foreignKey: 'category_id',
      as: 'categoryDetails',
    });

    // Product has many order items
    this.hasMany(models.OrderItem, {
      foreignKey: 'product_id',
      as: 'orderItems',
    });

    // Product has many reviews
    this.hasMany(models.Review, {
      foreignKey: 'product_id',
      as: 'reviews',
    });
  }

  static hooks() {
    return {
      beforeCreate: async product => {
        // Generate SKU if not provided
        if (!product.sku) {
          product.sku = await Product.generateSku(product.name);
        }
      },
      beforeUpdate: async product => {
        // Log price changes
        if (product.changed('price')) {
          console.log(
            `Price changed for ${product.name}: ${product._previousDataValues.price} → ${product.price}`,
          );
        }
      },
    };
  }

  static options() {
    return {
      indexes: [
        {
          fields: ['category'],
        },
        {
          fields: ['sku'],
          unique: true,
        },
        {
          fields: ['isActive', 'category'],
        },
      ],
    };
  }

  // Static methods
  static async generateSku(name) {
    const prefix = name.substring(0, 3).toUpperCase();
    const timestamp = Date.now().toString().slice(-6);
    return `${prefix}${timestamp}`;
  }

  static async findActiveProducts() {
    return this.findAll({
      where: { isActive: true },
      order: [['name', 'ASC']],
    });
  }

  // Instance methods
  async updateStock(quantity) {
    this.stockQuantity += quantity;
    await this.save();
  }

  isInStock() {
    return this.stockQuantity > 0;
  }

  async getAverageRating() {
    const reviews = await this.getReviews();
    if (reviews.length === 0) return 0;

    const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
    return (sum / reviews.length).toFixed(1);
  }
}

module.exports = Product;
```

### Step 2: Create Migration

Create a migration file using Sequelize CLI:

```bash
npx sequelize-cli migration:generate --name create-products
```

```javascript
// src/migrations/YYYYMMDDHHMMSS-create-products.js
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('products', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      category: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      sku: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      stock_quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      tags: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Add indexes
    await queryInterface.addIndex('products', ['category']);
    await queryInterface.addIndex('products', ['sku'], { unique: true });
    await queryInterface.addIndex('products', ['is_active', 'category']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('products');
  },
};
```

## Data Types and Validation

### Common Data Types

```javascript
static schema() {
  return {
    // String types
    name: {
      type: DataTypes.STRING,        // VARCHAR(255)
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,          // TEXT
      allowNull: true
    },
    code: {
      type: DataTypes.STRING(10),    // VARCHAR(10)
      allowNull: false
    },

    // Number types
    price: {
      type: DataTypes.DECIMAL(10, 2), // DECIMAL(10,2)
      allowNull: false
    },
    quantity: {
      type: DataTypes.INTEGER,        // INTEGER
      allowNull: false,
      defaultValue: 0
    },
    rating: {
      type: DataTypes.FLOAT,          // FLOAT
      allowNull: true
    },

    // Boolean type
    isActive: {
      type: DataTypes.BOOLEAN,        // BOOLEAN
      allowNull: false,
      defaultValue: true
    },

    // Date types
    birthDate: {
      type: DataTypes.DATEONLY,       // DATE (YYYY-MM-DD)
      allowNull: true
    },
    lastLoginAt: {
      type: DataTypes.DATE,           // DATETIME
      allowNull: true
    },

    // JSON type
    metadata: {
      type: DataTypes.JSON,           // JSON
      allowNull: true,
      defaultValue: {}
    },

    // Enum type
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'pending'),
      allowNull: false,
      defaultValue: 'pending'
    }
  };
}
```

### Validation Rules

```javascript
static schema() {
  return {
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
        notEmpty: true
      }
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [8, 255],
        notEmpty: true
      }
    },
    age: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0,
        max: 150
      }
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true
      }
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        is: /^[\+]?[1-9][\d]{0,15}$/  // Regex validation
      }
    },
    customField: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        customValidator(value) {
          if (value.length < 5) {
            throw new Error('Custom field must be at least 5 characters');
          }
        }
      }
    }
  };
}
```

## Associations

### One-to-Many Relationships

```javascript
// User has many Orders
class User extends AppModel {
  static associate(models) {
    this.hasMany(models.Order, {
      foreignKey: 'user_id',
      as: 'orders',
    });
  }
}

// Order belongs to User
class Order extends AppModel {
  static associate(models) {
    this.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });
  }
}
```

### Many-to-Many Relationships

```javascript
// Product belongs to many Categories through ProductCategory
class Product extends AppModel {
  static associate(models) {
    this.belongsToMany(models.Category, {
      through: models.ProductCategory,
      foreignKey: 'product_id',
      otherKey: 'category_id',
      as: 'categories',
    });
  }
}

// Category belongs to many Products through ProductCategory
class Category extends AppModel {
  static associate(models) {
    this.belongsToMany(models.Product, {
      through: models.ProductCategory,
      foreignKey: 'category_id',
      otherKey: 'product_id',
      as: 'products',
    });
  }
}

// Junction table
class ProductCategory extends AppModel {
  static schema() {
    return {
      product_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'products',
          key: 'id',
        },
      },
      category_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'categories',
          key: 'id',
        },
      },
    };
  }
}
```

### Self-Referencing Relationships

```javascript
// User can have a manager (another User)
class User extends AppModel {
  static associate(models) {
    // User belongs to manager
    this.belongsTo(models.User, {
      foreignKey: 'manager_id',
      as: 'manager',
    });

    // User has many subordinates
    this.hasMany(models.User, {
      foreignKey: 'manager_id',
      as: 'subordinates',
    });
  }
}
```

## Lifecycle Hooks

### Common Hooks

```javascript
static hooks() {
  return {
    // Before validation
    beforeValidate: async (instance, options) => {
      // Normalize data before validation
      if (instance.email) {
        instance.email = instance.email.toLowerCase().trim();
      }
    },

    // After validation
    afterValidate: async (instance, options) => {
      // Additional validation logic
    },

    // Before create
    beforeCreate: async (instance, options) => {
      // Hash password, generate IDs, etc.
      if (instance.password) {
        instance.password_digest = await bcrypt.hash(instance.password, 12);
      }
    },

    // After create
    afterCreate: async (instance, options) => {
      // Send welcome email, create related records, etc.
      console.log(`Created new user: ${instance.email}`);
    },

    // Before update
    beforeUpdate: async (instance, options) => {
      // Handle password changes
      if (instance.changed('password')) {
        instance.password_digest = await bcrypt.hash(instance.password, 12);
      }
    },

    // After update
    afterUpdate: async (instance, options) => {
      // Log changes, send notifications, etc.
      console.log(`Updated user: ${instance.email}`);
    },

    // Before destroy
    beforeDestroy: async (instance, options) => {
      // Clean up related data
      await instance.getOrders().then(orders =>
        Promise.all(orders.map(order => order.destroy()))
      );
    },

    // After destroy
    afterDestroy: async (instance, options) => {
      // Log deletion, send notifications, etc.
      console.log(`Deleted user: ${instance.email}`);
    }
  };
}
```

## Querying Models

### Basic Queries

```javascript
// Find all
const users = await User.findAll();

// Find by primary key
const user = await User.findByPk(1);

// Find one
const user = await User.findOne({
  where: { email: '<EMAIL>' },
});

// Find or create
const [user, created] = await User.findOrCreate({
  where: { email: '<EMAIL>' },
  defaults: { name: 'New User' },
});

// Count
const count = await User.count({
  where: { isActive: true },
});
```

### Advanced Queries

```javascript
// Complex where conditions
const products = await Product.findAll({
  where: {
    price: {
      [Op.between]: [10, 100],
    },
    category: {
      [Op.in]: ['electronics', 'books'],
    },
    name: {
      [Op.like]: '%phone%',
    },
  },
});

// Include associations
const orders = await Order.findAll({
  include: [
    {
      model: User,
      as: 'user',
      attributes: ['id', 'name', 'email'],
    },
    {
      model: OrderItem,
      as: 'items',
      include: [
        {
          model: Product,
          as: 'product',
        },
      ],
    },
  ],
});

// Pagination and ordering
const products = await Product.findAndCountAll({
  where: { isActive: true },
  order: [['name', 'ASC']],
  limit: 10,
  offset: 20,
});
```

## Testing Models

```javascript
// tests/models/Product.test.js
const { Product } = require('../../src/models');
const { sequelize } = require('../../src/config/sequelize');

describe('Product Model', () => {
  beforeAll(async () => {
    await sequelize.sync({ force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    await Product.destroy({ where: {}, truncate: true });
  });

  describe('validation', () => {
    it('should create a valid product', async () => {
      const productData = {
        name: 'Test Product',
        price: 99.99,
        category: 'electronics',
        sku: 'TEST001',
      };

      const product = await Product.create(productData);
      expect(product.id).toBeDefined();
      expect(product.name).toBe('Test Product');
    });

    it('should fail validation for invalid price', async () => {
      const productData = {
        name: 'Test Product',
        price: -10,
        category: 'electronics',
        sku: 'TEST001',
      };

      await expect(Product.create(productData)).rejects.toThrow();
    });
  });

  describe('instance methods', () => {
    it('should check if product is in stock', async () => {
      const product = await Product.create({
        name: 'Test Product',
        price: 99.99,
        category: 'electronics',
        sku: 'TEST001',
        stockQuantity: 5,
      });

      expect(product.isInStock()).toBe(true);
    });
  });
});
```

## Best Practices

### 1. Follow Naming Conventions

```javascript
// ✅ Good - consistent naming
class UserProfile extends AppModel {
  static schema() {
    return {
      firstName: DataTypes.STRING, // camelCase in model
      lastName: DataTypes.STRING, // maps to first_name in DB
      isActive: DataTypes.BOOLEAN, // maps to is_active in DB
    };
  }
}
```

### 2. Use Appropriate Data Types

```javascript
// ✅ Good - specific data types
price: {
  type: DataTypes.DECIMAL(10, 2),  // For currency
  allowNull: false
},
quantity: {
  type: DataTypes.INTEGER,         // For counts
  allowNull: false
},
metadata: {
  type: DataTypes.JSON,            // For flexible data
  allowNull: true
}
```

### 3. Add Proper Validation

```javascript
// ✅ Good - comprehensive validation
email: {
  type: DataTypes.STRING,
  allowNull: false,
  unique: true,
  validate: {
    isEmail: true,
    notEmpty: true,
    len: [1, 255]
  }
}
```

### 4. Use Indexes Strategically

```javascript
static options() {
  return {
    indexes: [
      { fields: ['email'], unique: true },      // Unique constraint
      { fields: ['category'] },                 // Query optimization
      { fields: ['isActive', 'category'] },     // Composite index
      { fields: ['createdAt'] }                 // Sorting optimization
    ]
  };
}
```

### 5. Keep Business Logic in Services

```javascript
// ✅ Good - simple model methods
class Product extends AppModel {
  isInStock() {
    return this.stockQuantity > 0;
  }

  getDisplayPrice() {
    return `$${this.price.toFixed(2)}`;
  }
}

// ❌ Bad - complex business logic in model
class Product extends AppModel {
  async processOrder(quantity, userId) {
    // This should be in a service
    if (this.stockQuantity < quantity) {
      throw new Error('Insufficient stock');
    }

    this.stockQuantity -= quantity;
    await this.save();

    // Create order, send emails, etc.
  }
}
```
