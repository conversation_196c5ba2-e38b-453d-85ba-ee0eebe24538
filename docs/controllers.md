# Controllers

Controllers handle HTTP requests and coordinate between the input validation,
business logic (services), and output formatting layers. All controllers extend
the `ApiController` base class.

## ApiController Base Class

The `ApiController` provides common functionality for all controllers:

### Key Features

- **Error Handling**: Automatic error catching and consistent error responses
- **Method Wrapping**: The `createMethod()` wrapper handles async errors
- **HTTP Status Mapping**: Maps custom error types to appropriate HTTP status
  codes
- **Development Support**: Includes stack traces in non-production environments

### Error Handling

The base controller automatically handles these error types:

- `InvalidError` → 400 Bad Request
- `UnauthorizedError` → 401 Unauthorized
- `ForbiddenError` → 403 Forbidden
- `NotFoundError` → 404 Not Found
- Any other error → 500 Internal Server Error

## Controller Structure

### Basic Controller Pattern

```javascript
const ApiController = require('./ApiController');
const SomeService = require('../services/SomeService');
const SomeInput = require('../inputs/SomeInput');
const SomeOutput = require('../outputs/SomeOutput');

class SomeController extends ApiController {
  constructor() {
    super();
    this.service = new SomeService();
  }

  // Use createMethod to wrap all controller methods
  methodName = this.createMethod(async (req, res) => {
    // 1. Validate input
    const input = new SomeInput(req.body);
    input.validate();

    // 2. Call service with validated data
    const result = await this.service.someMethod(input.output());

    // 3. Format and send response
    const output = new SomeOutput(result);
    output.renderJson(res);
  });
}

module.exports = new SomeController();
```

### Method Wrapping

Always wrap controller methods with `createMethod()`:

```javascript
// ✅ Correct - wrapped method
methodName = this.createMethod(async (req, res) => {
  // Your logic here
});

// ❌ Wrong - unwrapped method (errors won't be handled)
async methodName(req, res) {
  // Your logic here
}
```

## Creating a New Controller

### Step 1: Create the Controller File

Create a new file in `src/controllers/` following the naming convention
`{Name}Controller.js`:

```javascript
// src/controllers/ProductController.js
const ApiController = require('./ApiController');
const ProductService = require('../services/ProductService');
const CreateProductInput = require('../inputs/CreateProductInput');
const ProductOutput = require('../outputs/ProductOutput');

class ProductController extends ApiController {
  constructor() {
    super();
    this.service = new ProductService();
  }

  create = this.createMethod(async (req, res) => {
    const input = new CreateProductInput(req.body);
    input.validate();

    const product = await this.service.create(input.output());
    const output = new ProductOutput(product);

    output.renderJson(res);
  });

  show = this.createMethod(async (req, res) => {
    const { id } = req.params;
    const product = await this.service.findById(id);

    const output = new ProductOutput(product);
    output.renderJson(res);
  });

  index = this.createMethod(async (req, res) => {
    const products = await this.service.findAll();
    const output = new ProductOutput(products);

    output.renderJsonArray(res);
  });
}

module.exports = new ProductController();
```

### Step 2: Add Routes

Create or update the route file in `src/routes/`:

```javascript
// src/routes/products.js
const express = require('express');
const productController = require('../controllers/ProductController');

const router = express.Router();

router.post('/', productController.create);
router.get('/:id', productController.show);
router.get('/', productController.index);

module.exports = router;
```

### Step 3: Register Routes in App

Add the routes to `src/app.js`:

```javascript
// src/app.js
const productRoutes = require('./routes/products');

// Add this line with other route registrations
app.use('/api/v1/products', rateLimiters.general, productRoutes);
```

## Controller Best Practices

### 1. Keep Controllers Thin

Controllers should only:

- Validate input
- Call services
- Format output
- Handle HTTP-specific concerns

```javascript
// ✅ Good - thin controller
create = this.createMethod(async (req, res) => {
  const input = new CreateUserInput(req.body);
  input.validate();

  const user = await this.service.create(input.output());
  const output = new UserOutput(user);

  output.renderJson(res);
});

// ❌ Bad - business logic in controller
create = this.createMethod(async (req, res) => {
  const { email, password } = req.body;

  // This business logic should be in a service
  if (await User.findOne({ where: { email } })) {
    throw new InvalidError('Email already exists');
  }

  const hashedPassword = await bcrypt.hash(password, 12);
  const user = await User.create({ email, password: hashedPassword });

  res.json({ data: user });
});
```

### 2. Use Consistent Naming

- Controller files: `{Name}Controller.js`
- Controller classes: `{Name}Controller`
- Method names: Use REST conventions (`index`, `show`, `create`, `update`,
  `destroy`)

### 3. Handle Different Response Types

```javascript
// Single item response
const output = new SomeOutput(item);
output.renderJson(res);

// Array response
const output = new SomeOutput(items);
output.renderJsonArray(res);

// Custom status code
const output = new SomeOutput(item, { statusCode: 201 });
output.renderJson(res);
```

### 4. Access Request Data Properly

```javascript
methodName = this.createMethod(async (req, res) => {
  // URL parameters
  const { id } = req.params;

  // Query parameters
  const { page, limit } = req.query;

  // Request body
  const input = new SomeInput(req.body);

  // Headers (if needed)
  const authToken = req.headers.authorization;
});
```

## Testing Controllers

Controllers should be tested using integration tests with supertest:

```javascript
// tests/controllers/ProductController.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('ProductController', () => {
  describe('POST /api/v1/products', () => {
    it('should create a product with valid data', async () => {
      const productData = {
        name: 'Test Product',
        price: 99.99,
      };

      const response = await request(app)
        .post('/api/v1/products')
        .send(productData);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.name).toBe('Test Product');
    });
  });
});
```

## Common Patterns

### Authentication Required

```javascript
// Add authentication middleware to routes
router.post('/', authMiddleware, productController.create);

// Access authenticated user in controller
create = this.createMethod(async (req, res) => {
  const userId = req.user.id; // Set by auth middleware
  // ... rest of logic
});
```

### Pagination

```javascript
index = this.createMethod(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  const offset = (page - 1) * limit;

  const result = await this.service.findAll({ limit, offset });
  const output = new ProductOutput(result.items, {
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.total,
    },
  });

  output.renderJsonArray(res);
});
```

### File Uploads

```javascript
// Use multer middleware for file uploads
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });

// In routes
router.post('/upload', upload.single('file'), productController.upload);

// In controller
upload = this.createMethod(async (req, res) => {
  const file = req.file;
  const result = await this.service.processFile(file);

  const output = new FileOutput(result);
  output.renderJson(res);
});
```
