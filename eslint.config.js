const js = require('@eslint/js');
const globals = require('globals');
const prettier = require('eslint-plugin-prettier');
const prettierConfig = require('eslint-config-prettier');

module.exports = [
  // JavaScript files configuration
  {
    files: ['**/*.{js,mjs,cjs}'],
    plugins: {
      prettier,
    },
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.es2022,
        ...globals.jest,
      },
      ecmaVersion: 2022,
      sourceType: 'commonjs',
    },
    rules: {
      ...js.configs.recommended.rules,
      ...prettierConfig.rules,
      // Prettier integration
      'prettier/prettier': 'error',
      // Node.js specific rules
      'no-console': 'off', // Allow console.log in Node.js
      'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
      'prefer-const': 'error',
      'no-var': 'error',
      'object-shorthand': 'error',
      'prefer-template': 'error',
      // Express/API specific
      'no-process-exit': 'warn', // Allow in server.js for graceful shutdown
      'handle-callback-err': 'error',
    },
  },
  // Ignore patterns
  {
    ignores: [
      'node_modules/**',
      'dist/**',
      'build/**',
      'coverage/**',
      '.nyc_output/**',
      '*.min.js',
      'package-lock.json',
      'src/config/database.json',
      '.env*',
    ],
  },
];
