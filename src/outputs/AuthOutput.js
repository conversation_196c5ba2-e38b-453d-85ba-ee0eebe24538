const ApiOutput = require('./ApiOutput');
const UserOutput = require('./UserOutput');

/**
 * Output formatting class for authentication responses
 */
class AuthOutput extends ApiOutput {
  /**
   * Format the authentication output data
   * @returns {Object} Formatted authentication data
   */
  format() {
    return {
      authToken: this.data,
      user: this.userOutput(this.options.user),
    };
  }

  /**
   * Format user data for output
   * @param {Object} user - User object
   * @returns {Object} Formatted user data
   */
  userOutput(user) {
    if (!user) return {};

    const output = new UserOutput(user);
    return output.format();
  }
}

module.exports = AuthOutput;
