/**
 * Base class for all output formatting classes
 * Provides common output formatting functionality
 */
class ApiOutput {
  constructor(data, options = {}) {
    this.data = data;
    this.options = options;
  }

  /**
   * Render the output data as JSON response
   * @param {Object} res - Express response object
   */
  renderJson(res) {
    const status = this.options.statusCode || 200;
    const formatMethod = this.options.formatMethod || 'format';

    const data = this[formatMethod]();
    res.status(status).json({ data });
  }

  /**
   * Render the output data as JSON response
   * @param {Object} res - Express response object
   */
  renderJsonArray(res) {
    const status = this.options.statusCode || 200;
    const formatMethod = this.options.formatMethod || 'format';

    const data = this.data.map(item => this[formatMethod](item));
    res.status(status).json({ data });
  }
}

module.exports = ApiOutput;
