const request = require('supertest');
const express = require('express');
const { createRateLimiter, rateLimiters } = require('../../src/middlewares/rateLimiter');

describe('Rate Limiter Middleware', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
  });

  describe('createRateLimiter', () => {
    it('should create a rate limiter with default configuration', () => {
      const limiter = createRateLimiter();
      expect(typeof limiter).toBe('function');
    });

    it('should create a rate limiter with custom configuration', () => {
      const limiter = createRateLimiter({
        windowMs: 60000,
        max: 10,
      });
      expect(typeof limiter).toBe('function');
    });
  });

  describe('Rate limiting functionality', () => {
    beforeEach(() => {
      // Create a test route with rate limiting
      const testLimiter = createRateLimiter({
        windowMs: 60000, // 1 minute
        max: 3, // 3 requests per minute
      });

      app.use('/test', testLimiter);
      app.get('/test', (req, res) => {
        res.json({ message: 'Success' });
      });
    });

    it('should allow requests within the limit', async () => {
      const response = await request(app).get('/test').expect(200);

      expect(response.body.message).toBe('Success');
      expect(response.headers['x-ratelimit-limit']).toBe('3');
      expect(response.headers['x-ratelimit-remaining']).toBeDefined();
      expect(response.headers['x-ratelimit-reset']).toBeDefined();
    });

    it('should block requests exceeding the limit', async () => {
      // Make requests up to the limit
      for (let i = 0; i < 3; i++) {
        await request(app).get('/test').expect(200);
      }

      // The 4th request should be rate limited
      const response = await request(app).get('/test').expect(429);

      expect(response.body.error).toBe('Too Many Requests');
      expect(response.body.message).toBe('Rate limit exceeded. Please try again later.');
      expect(response.headers['x-ratelimit-limit']).toBe('3');
      expect(response.headers['x-ratelimit-remaining']).toBe('0');
      expect(response.headers['x-ratelimit-reset']).toBeDefined();
    });

    it('should include required headers in all responses', async () => {
      const response = await request(app).get('/test').expect(200);

      // Check for required headers
      expect(response.headers['x-ratelimit-limit']).toBeDefined();
      expect(response.headers['x-ratelimit-remaining']).toBeDefined();
      expect(response.headers['x-ratelimit-reset']).toBeDefined();
    });
  });

  describe('Health check endpoint exclusion', () => {
    beforeEach(() => {
      const testLimiter = createRateLimiter({
        windowMs: 60000,
        max: 1, // Very restrictive limit
      });

      app.use(testLimiter);
      app.get('/api/v1/health', (req, res) => {
        res.json({ status: 'OK' });
      });
      app.get('/test', (req, res) => {
        res.json({ message: 'Success' });
      });
    });

    it('should skip rate limiting for health check endpoint', async () => {
      // Make multiple requests to health check - should not be rate limited
      for (let i = 0; i < 5; i++) {
        await request(app).get('/api/v1/health').expect(200);
      }

      // Regular endpoint should still be rate limited
      await request(app).get('/test').expect(200);
      await request(app).get('/test').expect(429);
    });
  });

  describe('Pre-configured rate limiters', () => {
    it('should have general rate limiter', () => {
      expect(typeof rateLimiters.general).toBe('function');
    });

    it('should have auth rate limiter', () => {
      expect(typeof rateLimiters.auth).toBe('function');
    });

    it('should have public rate limiter', () => {
      expect(typeof rateLimiters.public).toBe('function');
    });
  });

  describe('Environment configuration', () => {
    const originalEnv = process.env;

    beforeEach(() => {
      jest.resetModules();
      process.env = { ...originalEnv };
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('should use environment variables for configuration', () => {
      process.env.RATE_LIMIT_WINDOW_MS = '30000';
      process.env.RATE_LIMIT_MAX_REQUESTS = '50';

      // Re-require the module to pick up new env vars
      const {
        createRateLimiter: newCreateRateLimiter,
      } = require('../../src/middlewares/rateLimiter');

      const limiter = newCreateRateLimiter();
      expect(typeof limiter).toBe('function');
    });
  });

  describe('Custom key generator', () => {
    it('should use IP address as default key', async () => {
      const testLimiter = createRateLimiter({
        windowMs: 60000,
        max: 1,
      });

      app.use('/test', testLimiter);
      app.get('/test', (req, res) => {
        res.json({ message: 'Success' });
      });

      // First request should succeed
      await request(app).get('/test').expect(200);

      // Second request from same IP should be rate limited
      await request(app).get('/test').expect(429);
    });
  });
});
